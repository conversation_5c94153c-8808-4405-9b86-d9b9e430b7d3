#!/bin/bash
set -e
set -o pipefail

# ======================================================
# restart.sh - Safely restart all Docker services
# ======================================================

# Color definitions for status messages
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Configuration variables
DOCKER_TIMEOUT=30

# Service directories
TRAEFIK_DIR="${HOME}/_traefik"
POSTGRES_DIR="${HOME}/postgres"
REDIS_DIR="${HOME}/redis"
LLM_DIR="${HOME}/llm"
BACKEND_DIR="${HOME}/backend"
FRONTEND_DIR="${HOME}/frontend"
MONITORING_DIR="${HOME}/monitoring"

# Function to display colored status messages
log() {
    local level=$1
    local message=$2

    case $level in
        "info")
            echo -e "${GREEN}[INFO]${NC} $message"
            ;;
        "warn")
            echo -e "${YELLOW}[WARNING]${NC} $message"
            ;;
        "error")
            echo -e "${RED}[ERROR]${NC} $message"
            ;;
        *)
            echo -e "$message"
            ;;
    esac
}

# Function to check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        log "error" "Docker is not installed. Please install Docker first."
        log "info" "Visit https://docs.docker.com/get-docker/ for installation instructions."
        exit 1
    fi

    # Check if Docker daemon is running
    if ! docker info &> /dev/null; then
        log "error" "Docker daemon is not running. Please start Docker first."
        exit 1
    fi

    log "info" "Docker is installed and running."
}

# Function to check if Docker Compose is installed
check_docker_compose() {
    if ! docker compose version &> /dev/null; then
        log "error" "Docker Compose is not installed. Please install Docker Compose first."
        log "info" "Visit https://docs.docker.com/compose/install/ for installation instructions."
        exit 1
    fi

    log "info" "Docker Compose is installed."
}

# Function to stop all running containers
stop_containers() {
    log "info" "Stopping all running containers with a ${DOCKER_TIMEOUT}-second timeout..."

    # Stop services in reverse order
    for dir in "$FRONTEND_DIR" "$BACKEND_DIR" "$LLM_DIR/langgraph" "$LLM_DIR/intent" "$LLM_DIR/information" "$LLM_DIR/tool" "$LLM_DIR/postgres-state" "$LLM_DIR/postgres-db" "$REDIS_DIR" "$POSTGRES_DIR" "$TRAEFIK_DIR" "$MONITORING_DIR"; do
        if [ -d "$dir" ] && [ -f "$dir/docker-compose.yml" -o -f "$dir/docker-compose.yaml" ]; then
            log "info" "Stopping services in $dir..."
            (cd "$dir" && docker compose down --timeout $DOCKER_TIMEOUT) || log "warn" "Failed to stop services in $dir"
        fi
    done

    log "info" "All containers stopped."
}

# Function to clean up Docker resources
clean_resources() {
    log "info" "Cleaning up Docker resources..."

    # Remove dangling volumes
    log "info" "Removing dangling volumes..."
    docker volume prune -f

    # Remove dangling networks
    log "info" "Removing dangling networks..."
    docker network prune -f

    # Remove dangling images
    log "info" "Removing dangling images..."
    docker image prune -f

    log "info" "Docker resources cleaned up."
}

# Function to start all services
start_services() {
    log "info" "Starting all services..."

    # Start services in order
    for dir in "$TRAEFIK_DIR" "$POSTGRES_DIR" "$REDIS_DIR" "$LLM_DIR/postgres-db" "$LLM_DIR/postgres-state" "$LLM_DIR/tool" "$LLM_DIR/information" "$LLM_DIR/intent" "$LLM_DIR/langgraph" "$BACKEND_DIR" "$FRONTEND_DIR" "$MONITORING_DIR"; do
        if [ -d "$dir" ] && [ -f "$dir/docker-compose.yml" -o -f "$dir/docker-compose.yaml" ]; then
            # Check if a tag file exists for this service
            if [ -f "$dir/tag" ]; then
                # Read the tag from the file
                local service_tag=$(cat "$dir/tag" 2>/dev/null)

                if [ -n "$service_tag" ]; then
                    log "info" "Starting services in $dir with tag: $service_tag..."
                    (cd "$dir" && export tag="$service_tag" && docker compose up -d) || {
                        log "error" "Failed to start services in $dir"
                        return 2
                    }
                else
                    log "warn" "Tag file exists in $dir but is empty. Starting without tag..."
                    (cd "$dir" && docker compose up -d) || {
                        log "error" "Failed to start services in $dir"
                        return 2
                    }
                fi
            else
                log "info" "Starting services in $dir (no tag)..."
                (cd "$dir" && docker compose up -d) || {
                    log "error" "Failed to start services in $dir"
                    return 2
                }
            fi
        fi
    done

    log "info" "All services started successfully."
    return 0
}

# Function to display help/usage information
show_help() {
    echo "Usage: $0 [OPTIONS]"
    echo ""
    echo "Safely stop and restart all Docker services defined in the project."
    echo ""
    echo "Options:"
    echo "  -h, --help     Display this help message and exit"
    echo "  -c, --clean    Clean up dangling volumes, networks, and images before restarting"
    echo ""
    echo "Examples:"
    echo "  $0             # Restart all services"
    echo "  $0 --clean     # Clean up Docker resources and restart all services"
    echo "  $0 --help      # Display this help message"
    echo ""
    echo "Exit codes:"
    echo "  0 - Success"
    echo "  1 - Dependency error (Docker or Docker Compose not installed)"
    echo "  2 - Docker operation failure"
}

# Parse command-line arguments
CLEAN=false

while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_help
            exit 0
            ;;
        -c|--clean)
            CLEAN=true
            shift
            ;;
        *)
            log "error" "Unknown option: $1"
            show_help
            exit 1
            ;;
    esac
done

# Main script execution
main() {
    log "info" "Starting service restart process..."

    # Check dependencies
    check_docker
    check_docker_compose

    # Stop all running containers
    stop_containers

    # Clean up resources if requested
    if [ "$CLEAN" = true ]; then
        clean_resources
    fi

    # Start all services
    start_services
    exit_code=$?

    if [ $exit_code -eq 0 ]; then
        log "info" "Service restart completed successfully."
    else
        log "error" "Service restart failed with exit code $exit_code."
        exit $exit_code
    fi
}

# Execute main function
main
